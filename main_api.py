"""
木偶AI翻唱 FastAPI 后端服务
将原PyQt5应用程序转换为FastAPI后端，提供所有核心功能的API端点
"""

import os
import sys
import json
import time
import shutil
import tempfile
import subprocess
import threading
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import asyncio
from concurrent.futures import ThreadPoolExecutor

# FastAPI相关导入
from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks, Form
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# 音频处理相关导入
import numpy as np
from pydub import AudioSegment

# 设置FFmpeg路径
FFMPEG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ffmpeg", "bin", "ffmpeg.exe")
if not os.path.exists(FFMPEG_PATH):
    FFMPEG_PATH = "ffmpeg"

# 配置AudioSegment的FFmpeg路径
AudioSegment.converter = FFMPEG_PATH

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('MuOuAPI')

# 创建必要的目录
for directory in ["models", "input", "results", "outputs"]:
    os.makedirs(directory, exist_ok=True)

# 创建示例模型文件（如果不存在）
if not os.path.exists("models"):
    os.makedirs("models")
    for name in ["voice_model_alpha", "singer_beta", "another_model"]:
        with open(os.path.join("models", f"{name}.pt"), "w") as f:
            f.write("dummy pt")
        if name != "another_model":
            with open(os.path.join("models", f"{name}.yaml"), "w") as f:
                f.write("dummy yaml")
    with open("models/config_only.yaml", "w") as f:
        f.write("dummy yaml")

# ==================== Pydantic 模型定义 ====================

class ProcessingParams(BaseModel):
    """处理参数模型"""
    processing_mode: str = Field(default="完整模式", description="处理模式：完整模式或干声模式")
    output_format: str = Field(default="WAV", description="输出格式：WAV或MP3")
    model_file: str = Field(..., description="音色模型文件名")
    config_file: Optional[str] = Field(default=None, description="配置文件名")
    vocal_pitch: int = Field(default=0, ge=-18, le=18, description="人声音高调整")
    instrumental_pitch: int = Field(default=0, ge=-18, le=18, description="伴奏音高调整")
    reverb_enabled: bool = Field(default=False, description="是否启用混响")
    room_size: float = Field(default=0.6, ge=0.0, le=1.0, description="房间大小")
    damping: float = Field(default=0.1, ge=0.0, le=1.0, description="阻尼")
    wet_level: float = Field(default=0.3, ge=0.0, le=1.0, description="湿润度")
    dry_level: float = Field(default=0.9, ge=0.0, le=1.0, description="干燥度")
    harmony_to_accomp: bool = Field(default=False, description="和声加入伴奏")
    vocoder: str = Field(default="pc_nsf_hifigan_testing", description="声码器")
    formant_shift: int = Field(default=0, ge=-6, le=6, description="共振峰偏移")
    method: str = Field(default="euler", description="采样器")
    f0_extractor: str = Field(default="rmvpe", description="F0提取器")
    infer_steps: int = Field(default=50, ge=1, le=1000, description="采样步数")
    device: str = Field(default="CUDA", description="设备选择")

class RemixParams(BaseModel):
    """重新混音参数模型"""
    room_size: float = Field(default=0.6, ge=0.0, le=1.0)
    damping: float = Field(default=0.1, ge=0.0, le=1.0)
    wet_level: float = Field(default=0.3, ge=0.0, le=1.0)
    dry_level: float = Field(default=0.9, ge=0.0, le=1.0)
    vocal_volume: int = Field(default=0, ge=-20, le=10, description="人声音量调整(dB)")
    instrumental_volume: int = Field(default=0, ge=-20, le=10, description="伴奏音量调整(dB)")
    harmony_to_accomp: bool = Field(default=False)

class TaskStatus(BaseModel):
    """任务状态模型"""
    task_id: str
    status: str  # pending, processing, completed, failed, stopped
    progress: float = Field(default=0.0, ge=0.0, le=100.0)
    message: str = ""
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class AudioFileInfo(BaseModel):
    """音频文件信息模型"""
    id: str
    title: str
    filepath: str
    metadata: Optional[str] = None

class SongInfo(BaseModel):
    """歌曲信息模型"""
    name: str
    dir_path: str
    main_file: str
    created_time: str
    model: str

# ==================== 全局变量和状态管理 ====================

# 任务管理
tasks: Dict[str, TaskStatus] = {}
task_processes: Dict[str, List[subprocess.Popen]] = {}
task_executor = ThreadPoolExecutor(max_workers=2)

# 当前上传的音频文件
current_audio_file: Optional[str] = None

# ==================== 辅助函数 ====================

def generate_task_id() -> str:
    """生成唯一的任务ID"""
    return f"task_{int(time.time() * 1000000)}"

def get_files_from_models_dir(extension: str = ".pt") -> List[str]:
    """获取models目录下的文件"""
    models_dir = "models"
    if not os.path.exists(models_dir):
        return []
    files = sorted([f for f in os.listdir(models_dir)
                   if f.endswith(extension) and os.path.isfile(os.path.join(models_dir, f))])
    return files

def run_subprocess_with_tracking(cmd: List[str], task_id: str, hide_console: bool = True) -> tuple:
    """执行子进程并跟踪到任务中"""
    creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' and hide_console else 0

    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        creationflags=creation_flags
    )

    # 将进程添加到任务跟踪中
    if task_id not in task_processes:
        task_processes[task_id] = []
    task_processes[task_id].append(process)

    stdout, stderr = process.communicate()
    return process.returncode, stdout, stderr

def stop_task_processes(task_id: str):
    """停止任务相关的所有进程"""
    if task_id not in task_processes:
        return

    killed_count = 0
    for process in task_processes[task_id]:
        try:
            if process and process.poll() is None:
                if sys.platform == 'win32':
                    subprocess.call(['taskkill', '/F', '/T', '/PID', str(process.pid)],
                                   stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL,
                                   creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    process.terminate()
                    time.sleep(0.1)
                    if process.poll() is None:
                        process.kill()
                killed_count += 1
        except Exception as e:
            logger.error(f"终止进程失败: {str(e)}")

    logger.info(f"已终止任务 {task_id} 的 {killed_count} 个进程")
    task_processes[task_id] = []

def add_reverb(audio: AudioSegment, room_size: float = 0.3, damping: float = 0.1,
               wet_level: float = 0.2, dry_level: float = 0.9) -> AudioSegment:
    """自定义混响函数"""
    y = np.array(audio.get_array_of_samples()).astype(np.float32)

    is_stereo = audio.channels == 2
    if is_stereo:
        y = y.reshape((-1, 2))

    delay_samples = int(room_size * audio.frame_rate * 0.2)
    decay = 1.0 - damping

    y_reverb = np.zeros_like(y)
    for i in range(1, 5):
        delay = int(delay_samples * i)
        if delay >= len(y):
            continue
        amplitude = decay ** i
        if is_stereo:
            y_reverb[delay:, 0] += y[:-delay, 0] * amplitude * wet_level
            y_reverb[delay:, 1] += y[:-delay, 1] * amplitude * wet_level
        else:
            y_reverb[delay:] += y[:-delay] * amplitude * wet_level

    y_out = np.clip(y * dry_level + y_reverb, -np.iinfo(np.int16).max, np.iinfo(np.int16).max).astype(np.int16)
    return audio._spawn(y_out)

# ==================== FastAPI 应用初始化 ====================

app = FastAPI(
    title="木偶AI翻唱 API",
    description="AI音色转换和音频处理服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ==================== API 端点定义 ====================

@app.get("/")
async def root():
    """根端点"""
    return {"message": "木偶AI翻唱 API 服务", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": time.time()}

# ==================== 文件管理端点 ====================

@app.post("/upload_audio/")
async def upload_audio(file: UploadFile = File(...)):
    """上传音频文件"""
    global current_audio_file

    # 检查文件类型
    if not file.filename.lower().endswith(('.wav', '.mp3', '.ogg')):
        raise HTTPException(status_code=400, detail="不支持的音频格式，请上传WAV、MP3或OGG文件")

    try:
        # 清理之前的上传文件
        if current_audio_file and os.path.exists(current_audio_file):
            os.unlink(current_audio_file)

        # 保存新文件
        timestamp = int(time.time())
        filename = f"uploaded_{timestamp}_{file.filename}"
        filepath = os.path.join("input", filename)

        with open(filepath, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        current_audio_file = filepath

        # 获取音频信息
        try:
            audio = AudioSegment.from_file(filepath)
            duration = len(audio) / 1000.0  # 转换为秒
            sample_rate = audio.frame_rate
            channels = audio.channels
        except Exception as e:
            logger.warning(f"无法获取音频信息: {str(e)}")
            duration = 0
            sample_rate = 0
            channels = 0

        return {
            "message": "文件上传成功",
            "filename": file.filename,
            "filepath": filepath,
            "size": len(content),
            "duration": duration,
            "sample_rate": sample_rate,
            "channels": channels
        }

    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.get("/selected_audio/")
async def get_selected_audio():
    """获取当前选定的音频文件信息"""
    global current_audio_file

    if not current_audio_file or not os.path.exists(current_audio_file):
        return {"message": "未选择音频文件", "filepath": None}

    try:
        # 获取文件信息
        stat = os.stat(current_audio_file)

        # 获取音频信息
        try:
            audio = AudioSegment.from_file(current_audio_file)
            duration = len(audio) / 1000.0
            sample_rate = audio.frame_rate
            channels = audio.channels
        except Exception as e:
            logger.warning(f"无法获取音频信息: {str(e)}")
            duration = 0
            sample_rate = 0
            channels = 0

        return {
            "filepath": current_audio_file,
            "filename": os.path.basename(current_audio_file),
            "size": stat.st_size,
            "duration": duration,
            "sample_rate": sample_rate,
            "channels": channels,
            "modified_time": stat.st_mtime
        }

    except Exception as e:
        logger.error(f"获取音频信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取音频信息失败: {str(e)}")

@app.delete("/selected_audio/")
async def clear_selected_audio():
    """清除当前选定的音频文件"""
    global current_audio_file

    if current_audio_file and os.path.exists(current_audio_file):
        try:
            os.unlink(current_audio_file)
            logger.info(f"已删除音频文件: {current_audio_file}")
        except Exception as e:
            logger.warning(f"删除音频文件失败: {str(e)}")

    current_audio_file = None
    return {"message": "已清除选定的音频文件"}

@app.get("/download_file/")
async def download_file(file_path: str):
    """下载指定的文件"""
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")

    if not os.path.isfile(file_path):
        raise HTTPException(status_code=400, detail="指定路径不是文件")

    # 安全检查：确保文件在允许的目录内
    allowed_dirs = ["outputs", "results"]
    file_abs_path = os.path.abspath(file_path)
    workspace_abs_path = os.path.abspath(".")

    is_allowed = False
    for allowed_dir in allowed_dirs:
        allowed_abs_path = os.path.abspath(allowed_dir)
        if file_abs_path.startswith(allowed_abs_path):
            is_allowed = True
            break

    if not is_allowed:
        raise HTTPException(status_code=403, detail="不允许访问此文件")

    return FileResponse(
        path=file_path,
        filename=os.path.basename(file_path),
        media_type='application/octet-stream'
    )

# ==================== 模型和配置管理端点 ====================

@app.get("/models/")
async def get_models():
    """获取所有可用的模型文件"""
    models = get_files_from_models_dir(".pt")
    if not models:
        return {"models": [], "message": "未找到模型文件"}
    return {"models": models}

@app.get("/configs/")
async def get_configs():
    """获取所有可用的配置文件"""
    configs = get_files_from_models_dir(".yaml")
    if not configs:
        return {"configs": [], "message": "未找到配置文件"}
    return {"configs": configs}

# ==================== 处理任务管理端点 ====================

@app.post("/process/start/")
async def start_processing(params: ProcessingParams, background_tasks: BackgroundTasks):
    """启动音频处理任务"""
    global current_audio_file

    # 检查是否有音频文件
    if not current_audio_file or not os.path.exists(current_audio_file):
        raise HTTPException(status_code=400, detail="请先上传音频文件")

    # 检查模型文件
    model_path = os.path.join("models", params.model_file)
    if not os.path.exists(model_path):
        raise HTTPException(status_code=400, detail=f"模型文件不存在: {params.model_file}")

    # 检查配置文件（如果指定）
    if params.config_file:
        config_path = os.path.join("models", params.config_file)
        if not os.path.exists(config_path):
            raise HTTPException(status_code=400, detail=f"配置文件不存在: {params.config_file}")

    # 生成任务ID
    task_id = generate_task_id()

    # 创建任务状态
    task_status = TaskStatus(
        task_id=task_id,
        status="pending",
        message="任务已创建，等待开始处理"
    )
    tasks[task_id] = task_status

    # 在后台启动处理任务
    background_tasks.add_task(process_audio_task, task_id, current_audio_file, params)

    return {"task_id": task_id, "message": "处理任务已启动"}

@app.post("/process/stop/")
async def stop_processing(task_id: str):
    """停止指定的处理任务"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = tasks[task_id]
    if task.status not in ["pending", "processing"]:
        raise HTTPException(status_code=400, detail="任务无法停止，当前状态: " + task.status)

    # 停止相关进程
    stop_task_processes(task_id)

    # 更新任务状态
    task.status = "stopped"
    task.message = "任务已被用户停止"

    return {"message": f"任务 {task_id} 已停止"}

@app.get("/process/status/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    return tasks[task_id]

@app.get("/process/results/{task_id}")
async def get_task_results(task_id: str):
    """获取任务结果"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = tasks[task_id]
    if task.status != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成")

    if not task.result:
        raise HTTPException(status_code=404, detail="任务结果不存在")

    return task.result

# ==================== 重新混音端点 ====================

@app.post("/remix/")
async def remix_audio(task_id: str, params: RemixParams):
    """重新混音已处理的音频"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = tasks[task_id]
    if task.status != "completed" or not task.result:
        raise HTTPException(status_code=400, detail="任务未完成或无结果可供重新混音")

    try:
        # 获取处理结果中的文件路径
        result = task.result
        converted_vocal = result.get("files", {}).get("converted_vocal")
        instrumental = result.get("files", {}).get("instrumental")
        harmony = result.get("files", {}).get("harmony")

        if not converted_vocal or not os.path.exists(converted_vocal):
            raise HTTPException(status_code=400, detail="未找到转换后的人声文件")

        if not instrumental or not os.path.exists(instrumental):
            raise HTTPException(status_code=400, detail="未找到伴奏文件")

        # 执行重新混音
        new_mix_file = await perform_remix(converted_vocal, instrumental, harmony, params, task_id)

        # 更新任务结果
        if "files" not in task.result:
            task.result["files"] = {}
        task.result["files"]["final_mix"] = new_mix_file
        task.message = "重新混音完成"

        return {"message": "重新混音完成", "mix_file": new_mix_file}

    except Exception as e:
        logger.error(f"重新混音失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重新混音失败: {str(e)}")

# ==================== 成品歌曲管理端点 ====================

@app.get("/songs/")
async def get_finished_songs():
    """获取所有成品歌曲列表"""
    try:
        outputs_dir = "outputs"
        if not os.path.exists(outputs_dir):
            return {"songs": [], "message": "outputs目录不存在"}

        songs = []
        song_folders = []

        # 遍历outputs目录下的所有子目录
        for item in os.listdir(outputs_dir):
            item_path = os.path.join(outputs_dir, item)
            if os.path.isdir(item_path):
                creation_time = os.path.getctime(item_path)
                song_folders.append((item, item_path, creation_time))

        # 按创建时间降序排序
        song_folders.sort(key=lambda x: x[2], reverse=True)

        # 处理每个歌曲目录
        for song_name, song_path, creation_time in song_folders:
            # 查找主文件（混音文件优先）
            main_file = None
            model_used = "未知模型"

            for file in os.listdir(song_path):
                if "混缩" in file or "混音" in file:
                    main_file = os.path.join(song_path, file)
                    break

            if not main_file:
                for file in os.listdir(song_path):
                    if "转换人声" in file:
                        main_file = os.path.join(song_path, file)
                        break

            if not main_file:
                for file in os.listdir(song_path):
                    if file.lower().endswith(('.wav', '.mp3', '.ogg')):
                        main_file = os.path.join(song_path, file)
                        break

            # 尝试从process_info.json获取模型信息
            process_info_path = os.path.join(song_path, "process_info.json")
            if os.path.exists(process_info_path):
                try:
                    with open(process_info_path, "r", encoding="utf-8") as f:
                        process_info = json.load(f)
                        model_used = process_info.get("model", "未知模型")
                except Exception as e:
                    logger.warning(f"读取处理信息失败: {str(e)}")

            if main_file:
                created_time_str = time.strftime("%Y/%m/%d %H:%M", time.localtime(creation_time))
                song_info = SongInfo(
                    name=song_name,
                    dir_path=song_path,
                    main_file=main_file,
                    created_time=created_time_str,
                    model=model_used
                )
                songs.append(song_info)

        return {"songs": [song.dict() for song in songs]}

    except Exception as e:
        logger.error(f"获取歌曲列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取歌曲列表失败: {str(e)}")

@app.get("/songs/{song_name}/files")
async def get_song_files(song_name: str):
    """获取指定歌曲的所有音频文件"""
    song_path = os.path.join("outputs", song_name)
    if not os.path.exists(song_path) or not os.path.isdir(song_path):
        raise HTTPException(status_code=404, detail="歌曲不存在")

    try:
        files = []
        audio_files = []

        # 分类收集音频文件
        mix_files = []
        vocal_files = []
        instrumental_files = []
        harmony_files = []
        other_files = []

        for file in os.listdir(song_path):
            file_path = os.path.join(song_path, file)
            if os.path.isfile(file_path) and file.lower().endswith(('.wav', '.mp3', '.ogg')):
                if "混缩" in file or "混音" in file:
                    mix_files.append((file, file_path))
                elif "转换人声" in file:
                    vocal_files.append((file, file_path))
                elif "伴奏" in file:
                    instrumental_files.append((file, file_path))
                elif "和声" in file:
                    harmony_files.append((file, file_path))
                else:
                    other_files.append((file, file_path))

        # 按优先级组合文件
        audio_files = mix_files + vocal_files + instrumental_files + harmony_files + other_files

        # 构建文件信息
        for idx, (file_name, file_path) in enumerate(audio_files, 1):
            display_name = os.path.splitext(file_name)[0]

            # 添加序号前缀
            prefix = ""
            if "混缩" in file_name or "混音" in file_name:
                prefix = "① "
            elif "转换人声" in file_name:
                prefix = "② "
            elif "伴奏" in file_name:
                prefix = "③ "
            elif "和声" in file_name:
                prefix = "④ "
            else:
                prefix = f"{idx} "

            file_info = AudioFileInfo(
                id=f"song_{idx}",
                title=f"{prefix}{display_name}",
                filepath=file_path,
                metadata=f"来源: {song_name}"
            )
            files.append(file_info)

        return {"files": [file.dict() for file in files]}

    except Exception as e:
        logger.error(f"获取歌曲文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取歌曲文件失败: {str(e)}")

@app.delete("/songs/{song_name}")
async def delete_song(song_name: str):
    """删除指定的歌曲"""
    song_path = os.path.join("outputs", song_name)
    if not os.path.exists(song_path) or not os.path.isdir(song_path):
        raise HTTPException(status_code=404, detail="歌曲不存在")

    try:
        # 删除整个歌曲目录
        shutil.rmtree(song_path)
        logger.info(f"已删除歌曲: {song_name}")
        return {"message": f"歌曲 {song_name} 已删除"}

    except Exception as e:
        logger.error(f"删除歌曲失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除歌曲失败: {str(e)}")

# ==================== 核心处理函数 ====================

async def perform_remix(converted_vocal: str, instrumental: str, harmony: Optional[str],
                       params: RemixParams, task_id: str) -> str:
    """执行重新混音操作"""
    try:
        # 读取音频文件
        vocal_audio = AudioSegment.from_file(converted_vocal)
        instrumental_audio = AudioSegment.from_file(instrumental)

        # 应用混响（如果需要）
        if params.room_size > 0 or params.wet_level > 0:
            vocal_audio = add_reverb(
                vocal_audio,
                room_size=params.room_size,
                damping=params.damping,
                wet_level=params.wet_level,
                dry_level=params.dry_level
            )

        # 调整音量
        vocal_audio = vocal_audio + params.vocal_volume
        instrumental_audio = instrumental_audio + params.instrumental_volume

        # 混合音频
        mixed_audio = vocal_audio.overlay(instrumental_audio)

        # 如果有和声且需要加入
        if params.harmony_to_accomp and harmony and os.path.exists(harmony):
            harmony_audio = AudioSegment.from_file(harmony)
            harmony_audio = harmony_audio + params.instrumental_volume
            mixed_audio = mixed_audio.overlay(harmony_audio)

        # 生成输出文件路径
        timestamp = int(time.time())
        output_filename = f"remix_{timestamp}.wav"

        # 确定输出目录（使用任务相关的输出目录）
        if task_id in tasks and tasks[task_id].result:
            result = tasks[task_id].result
            if "output_dir" in result:
                output_dir = result["output_dir"]
            else:
                output_dir = "outputs"
        else:
            output_dir = "outputs"

        output_path = os.path.join(output_dir, output_filename)

        # 导出混音文件
        mixed_audio.export(output_path, format="wav")

        logger.info(f"重新混音完成: {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"重新混音过程中出错: {str(e)}")
        raise Exception(f"重新混音失败: {str(e)}")

def process_audio_task(task_id: str, audio_file: str, params: ProcessingParams):
    """音频处理任务的主函数"""
    try:
        # 更新任务状态
        tasks[task_id].status = "processing"
        tasks[task_id].message = "开始处理音频"
        tasks[task_id].progress = 0.0

        # 获取原始文件名
        original_filename = os.path.splitext(os.path.basename(audio_file))[0]

        # 创建输出目录
        output_dir = os.path.join("outputs", original_filename)
        os.makedirs(output_dir, exist_ok=True)

        # 清空输出目录
        clear_directory(output_dir)

        processed_files = {}

        # 根据处理模式执行不同的流程
        if params.processing_mode == "完整模式":
            # 完整模式：分离 -> 转换 -> 混音
            tasks[task_id].message = "分离音频中..."
            tasks[task_id].progress = 10.0

            # 音频分离
            separated_files = process_audio_separation(audio_file, params, task_id)
            processed_files.update(separated_files)

            if tasks[task_id].status == "stopped":
                return

            tasks[task_id].message = "转换音色中..."
            tasks[task_id].progress = 40.0

            # 音色转换
            converted_vocal = convert_voice(separated_files.get("vocal"), params, task_id, output_dir)
            processed_files["converted_vocal"] = converted_vocal

            if tasks[task_id].status == "stopped":
                return

            tasks[task_id].message = "混音处理中..."
            tasks[task_id].progress = 70.0

            # 混音处理
            final_mix = create_final_mix(processed_files, params, task_id, output_dir)
            processed_files["final_mix"] = final_mix

            # 移动文件到输出目录
            move_files_to_output(processed_files, output_dir, original_filename)

        else:  # 干声模式
            tasks[task_id].message = "转换音色中..."
            tasks[task_id].progress = 20.0

            # 直接转换音色
            converted_vocal = convert_voice(audio_file, params, task_id, output_dir)
            processed_files["converted_vocal"] = converted_vocal

        if tasks[task_id].status == "stopped":
            return

        # 保存处理信息
        save_process_info(output_dir, params, original_filename)

        # 完成任务
        tasks[task_id].status = "completed"
        tasks[task_id].message = "处理完成"
        tasks[task_id].progress = 100.0
        tasks[task_id].result = {
            "output_dir": output_dir,
            "files": processed_files,
            "processing_mode": params.processing_mode
        }

        logger.info(f"任务 {task_id} 处理完成")

    except Exception as e:
        logger.error(f"任务 {task_id} 处理失败: {str(e)}")
        tasks[task_id].status = "failed"
        tasks[task_id].message = f"处理失败: {str(e)}"
        tasks[task_id].error = str(e)

def clear_directory(directory: str):
    """清空目录中的所有文件"""
    if not os.path.exists(directory):
        return

    for item in os.listdir(directory):
        item_path = os.path.join(directory, item)
        try:
            if os.path.isfile(item_path):
                os.unlink(item_path)
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)
        except Exception as e:
            logger.warning(f"删除文件/目录失败 {item_path}: {str(e)}")

def process_audio_separation(audio_file: str, params: ProcessingParams, task_id: str) -> Dict[str, str]:
    """音频分离处理"""
    try:
        # 清空input和results目录
        clear_directory("input")
        clear_directory("results")

        # 复制音频文件到input目录
        input_file = os.path.join("input", os.path.basename(audio_file))
        shutil.copy2(audio_file, input_file)

        # 构建分离命令
        python_exe = os.path.join(".", "workenv", "python.exe")
        cmd = [
            python_exe, './msst/scripts/preset_infer_cli.py',
            '-p', './msst/preset.json',
            '-i', 'input',
            '-o', 'results',
            '-f', params.output_format.lower(),
            '--extra_output_dir'
        ]

        logger.info(f"执行音频分离命令: {' '.join(cmd)}")

        # 执行命令
        returncode, stdout, stderr = run_subprocess_with_tracking(cmd, task_id)

        if tasks[task_id].status == "stopped":
            return {}

        if returncode != 0:
            raise Exception(f"音频分离失败: {stderr}")

        # 查找生成的文件
        original_filename = os.path.splitext(os.path.basename(audio_file))[0]
        output_format = params.output_format.lower()

        vocals_file = find_separation_file("results", original_filename, "vocals", output_format)
        instrumental_file = find_separation_file("results", original_filename, "other", output_format)
        harmony_file = find_separation_file("results/extra_output", original_filename, "vocals_other", output_format)

        if not vocals_file:
            raise Exception("未找到分离的人声文件")
        if not instrumental_file:
            raise Exception("未找到分离的伴奏文件")

        result = {
            "vocal": vocals_file,
            "instrumental": instrumental_file
        }

        if harmony_file:
            result["harmony"] = harmony_file

        return result

    except Exception as e:
        logger.error(f"音频分离失败: {str(e)}")
        raise Exception(f"音频分离失败: {str(e)}")

def find_separation_file(directory: str, filename: str, file_type: str, format: str) -> Optional[str]:
    """查找分离生成的文件"""
    if not os.path.exists(directory):
        return None

    # 精确匹配
    exact_file = os.path.join(directory, f"{filename}_{file_type}.{format}")
    if os.path.exists(exact_file):
        return exact_file

    # 模糊匹配
    for file in os.listdir(directory):
        if (filename in file and file_type in file and
            file.endswith(f".{format}")):
            return os.path.join(directory, file)

    return None

def convert_voice(input_file: str, params: ProcessingParams, task_id: str, output_dir: str) -> str:
    """音色转换处理"""
    try:
        if not input_file or not os.path.exists(input_file):
            raise Exception("输入音频文件不存在")

        # 构建转换命令
        python_exe = os.path.join(".", "workenv", "python.exe")

        # 基础命令参数
        cmd = [
            python_exe, './main_reflow.py',
            '-i', input_file,
            '-m', os.path.join("models", params.model_file),
            '-o', output_dir,
            '-k', str(params.vocal_pitch),
            '-ks', str(params.instrumental_pitch),
            '-e', params.f0_extractor,
            '-s', str(params.infer_steps),
            '-method', params.method,
            '-kf', str(params.formant_shift),
            '-device', params.device.lower(),
            '-vocoder', params.vocoder
        ]

        # 添加配置文件（如果指定）
        if params.config_file:
            cmd.extend(['-c', os.path.join("models", params.config_file)])

        logger.info(f"执行音色转换命令: {' '.join(cmd)}")

        # 执行命令
        returncode, stdout, stderr = run_subprocess_with_tracking(cmd, task_id)

        if tasks[task_id].status == "stopped":
            return ""

        if returncode != 0:
            raise Exception(f"音色转换失败: {stderr}")

        # 查找生成的转换文件
        original_filename = os.path.splitext(os.path.basename(input_file))[0]
        output_format = params.output_format.lower()

        # 查找转换后的文件
        for file in os.listdir(output_dir):
            if (file.startswith(original_filename) and
                "转换" in file and
                file.endswith(f".{output_format}")):
                return os.path.join(output_dir, file)

        # 如果没找到，查找任何音频文件
        for file in os.listdir(output_dir):
            if file.endswith(f".{output_format}"):
                return os.path.join(output_dir, file)

        raise Exception("未找到转换后的音频文件")

    except Exception as e:
        logger.error(f"音色转换失败: {str(e)}")
        raise Exception(f"音色转换失败: {str(e)}")

def create_final_mix(processed_files: Dict[str, str], params: ProcessingParams,
                    task_id: str, output_dir: str) -> str:
    """创建最终混音"""
    try:
        converted_vocal = processed_files.get("converted_vocal")
        instrumental = processed_files.get("instrumental")
        harmony = processed_files.get("harmony")

        if not converted_vocal or not os.path.exists(converted_vocal):
            raise Exception("转换后的人声文件不存在")

        if not instrumental or not os.path.exists(instrumental):
            raise Exception("伴奏文件不存在")

        # 读取音频文件
        vocal_audio = AudioSegment.from_file(converted_vocal)
        instrumental_audio = AudioSegment.from_file(instrumental)

        # 应用混响（如果启用）
        if params.reverb_enabled:
            vocal_audio = add_reverb(
                vocal_audio,
                room_size=params.room_size,
                damping=params.damping,
                wet_level=params.wet_level,
                dry_level=params.dry_level
            )

        # 混合音频
        mixed_audio = vocal_audio.overlay(instrumental_audio)

        # 如果有和声且需要加入伴奏
        if params.harmony_to_accomp and harmony and os.path.exists(harmony):
            harmony_audio = AudioSegment.from_file(harmony)
            mixed_audio = mixed_audio.overlay(harmony_audio)

        # 生成输出文件名
        original_filename = os.path.splitext(os.path.basename(converted_vocal))[0]
        output_filename = f"{original_filename}_混缩.{params.output_format.lower()}"
        output_path = os.path.join(output_dir, output_filename)

        # 导出混音文件
        mixed_audio.export(output_path, format=params.output_format.lower())

        logger.info(f"最终混音完成: {output_path}")
        return output_path

    except Exception as e:
        logger.error(f"创建最终混音失败: {str(e)}")
        raise Exception(f"创建最终混音失败: {str(e)}")

def move_files_to_output(processed_files: Dict[str, str], output_dir: str, original_filename: str):
    """移动处理后的文件到输出目录"""
    try:
        # 移动分离的文件到输出目录
        for file_type, file_path in processed_files.items():
            if file_path and os.path.exists(file_path):
                filename = os.path.basename(file_path)
                dest_path = os.path.join(output_dir, filename)

                # 如果文件不在输出目录中，则移动
                if os.path.abspath(file_path) != os.path.abspath(dest_path):
                    shutil.copy2(file_path, dest_path)
                    logger.info(f"已复制文件: {file_path} -> {dest_path}")

    except Exception as e:
        logger.warning(f"移动文件失败: {str(e)}")

def save_process_info(output_dir: str, params: ProcessingParams, original_filename: str):
    """保存处理信息到JSON文件"""
    try:
        process_info = {
            "original_filename": original_filename,
            "processing_mode": params.processing_mode,
            "model": params.model_file,
            "config": params.config_file,
            "vocal_pitch": params.vocal_pitch,
            "instrumental_pitch": params.instrumental_pitch,
            "reverb_enabled": params.reverb_enabled,
            "room_size": params.room_size,
            "damping": params.damping,
            "wet_level": params.wet_level,
            "dry_level": params.dry_level,
            "harmony_to_accomp": params.harmony_to_accomp,
            "vocoder": params.vocoder,
            "formant_shift": params.formant_shift,
            "method": params.method,
            "f0_extractor": params.f0_extractor,
            "infer_steps": params.infer_steps,
            "device": params.device,
            "output_format": params.output_format,
            "timestamp": time.time(),
            "date": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        info_file = os.path.join(output_dir, "process_info.json")
        with open(info_file, "w", encoding="utf-8") as f:
            json.dump(process_info, f, ensure_ascii=False, indent=2)

        logger.info(f"处理信息已保存: {info_file}")

    except Exception as e:
        logger.warning(f"保存处理信息失败: {str(e)}")

# ==================== 应用启动 ====================

if __name__ == "__main__":
    import uvicorn

    # 启动FastAPI应用
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
