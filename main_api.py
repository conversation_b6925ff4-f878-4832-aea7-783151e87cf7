"""
木偶AI翻唱 FastAPI 后端服务
将原PyQt5应用程序转换为FastAPI后端，提供所有核心功能的API端点
"""

import os
import sys
import json
import time
import shutil
import tempfile
import subprocess
import threading
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import asyncio
from concurrent.futures import ThreadPoolExecutor

# FastAPI相关导入
from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks, Form
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# 音频处理相关导入
import numpy as np
from pydub import AudioSegment

# 设置FFmpeg路径
FFMPEG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ffmpeg", "bin", "ffmpeg.exe")
if not os.path.exists(FFMPEG_PATH):
    FFMPEG_PATH = "ffmpeg"

# 配置AudioSegment的FFmpeg路径
AudioSegment.converter = FFMPEG_PATH

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('MuOuAPI')

# 创建必要的目录
for directory in ["models", "input", "results", "outputs"]:
    os.makedirs(directory, exist_ok=True)

# 创建示例模型文件（如果不存在）
if not os.path.exists("models"):
    os.makedirs("models")
    for name in ["voice_model_alpha", "singer_beta", "another_model"]:
        with open(os.path.join("models", f"{name}.pt"), "w") as f:
            f.write("dummy pt")
        if name != "another_model":
            with open(os.path.join("models", f"{name}.yaml"), "w") as f:
                f.write("dummy yaml")
    with open("models/config_only.yaml", "w") as f:
        f.write("dummy yaml")

# ==================== Pydantic 模型定义 ====================

class ProcessingParams(BaseModel):
    """处理参数模型"""
    processing_mode: str = Field(default="完整模式", description="处理模式：完整模式或干声模式")
    output_format: str = Field(default="WAV", description="输出格式：WAV或MP3")
    model_file: str = Field(..., description="音色模型文件名")
    config_file: Optional[str] = Field(default=None, description="配置文件名")
    vocal_pitch: int = Field(default=0, ge=-18, le=18, description="人声音高调整")
    instrumental_pitch: int = Field(default=0, ge=-18, le=18, description="伴奏音高调整")
    reverb_enabled: bool = Field(default=False, description="是否启用混响")
    room_size: float = Field(default=0.6, ge=0.0, le=1.0, description="房间大小")
    damping: float = Field(default=0.1, ge=0.0, le=1.0, description="阻尼")
    wet_level: float = Field(default=0.3, ge=0.0, le=1.0, description="湿润度")
    dry_level: float = Field(default=0.9, ge=0.0, le=1.0, description="干燥度")
    harmony_to_accomp: bool = Field(default=False, description="和声加入伴奏")
    vocoder: str = Field(default="pc_nsf_hifigan_testing", description="声码器")
    formant_shift: int = Field(default=0, ge=-6, le=6, description="共振峰偏移")
    method: str = Field(default="euler", description="采样器")
    f0_extractor: str = Field(default="rmvpe", description="F0提取器")
    infer_steps: int = Field(default=50, ge=1, le=1000, description="采样步数")
    device: str = Field(default="CUDA", description="设备选择")

class RemixParams(BaseModel):
    """重新混音参数模型"""
    room_size: float = Field(default=0.6, ge=0.0, le=1.0)
    damping: float = Field(default=0.1, ge=0.0, le=1.0)
    wet_level: float = Field(default=0.3, ge=0.0, le=1.0)
    dry_level: float = Field(default=0.9, ge=0.0, le=1.0)
    vocal_volume: int = Field(default=0, ge=-20, le=10, description="人声音量调整(dB)")
    instrumental_volume: int = Field(default=0, ge=-20, le=10, description="伴奏音量调整(dB)")
    harmony_to_accomp: bool = Field(default=False)

class TaskStatus(BaseModel):
    """任务状态模型"""
    task_id: str
    status: str  # pending, processing, completed, failed, stopped
    progress: float = Field(default=0.0, ge=0.0, le=100.0)
    message: str = ""
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class AudioFileInfo(BaseModel):
    """音频文件信息模型"""
    id: str
    title: str
    filepath: str
    metadata: Optional[str] = None

class SongInfo(BaseModel):
    """歌曲信息模型"""
    name: str
    dir_path: str
    main_file: str
    created_time: str
    model: str

# ==================== 全局变量和状态管理 ====================

# 任务管理
tasks: Dict[str, TaskStatus] = {}
task_processes: Dict[str, List[subprocess.Popen]] = {}
task_executor = ThreadPoolExecutor(max_workers=2)

# 当前上传的音频文件
current_audio_file: Optional[str] = None

# ==================== 辅助函数 ====================

def generate_task_id() -> str:
    """生成唯一的任务ID"""
    return f"task_{int(time.time() * 1000000)}"

def get_files_from_models_dir(extension: str = ".pt") -> List[str]:
    """获取models目录下的文件"""
    models_dir = "models"
    if not os.path.exists(models_dir):
        return []
    files = sorted([f for f in os.listdir(models_dir)
                   if f.endswith(extension) and os.path.isfile(os.path.join(models_dir, f))])
    return files

def run_subprocess_with_tracking(cmd: List[str], task_id: str, hide_console: bool = True) -> tuple:
    """执行子进程并跟踪到任务中"""
    creation_flags = subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' and hide_console else 0

    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        creationflags=creation_flags
    )

    # 将进程添加到任务跟踪中
    if task_id not in task_processes:
        task_processes[task_id] = []
    task_processes[task_id].append(process)

    stdout, stderr = process.communicate()
    return process.returncode, stdout, stderr

def stop_task_processes(task_id: str):
    """停止任务相关的所有进程"""
    if task_id not in task_processes:
        return

    killed_count = 0
    for process in task_processes[task_id]:
        try:
            if process and process.poll() is None:
                if sys.platform == 'win32':
                    subprocess.call(['taskkill', '/F', '/T', '/PID', str(process.pid)],
                                   stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL,
                                   creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    process.terminate()
                    time.sleep(0.1)
                    if process.poll() is None:
                        process.kill()
                killed_count += 1
        except Exception as e:
            logger.error(f"终止进程失败: {str(e)}")

    logger.info(f"已终止任务 {task_id} 的 {killed_count} 个进程")
    task_processes[task_id] = []

def add_reverb(audio: AudioSegment, room_size: float = 0.3, damping: float = 0.1,
               wet_level: float = 0.2, dry_level: float = 0.9) -> AudioSegment:
    """自定义混响函数"""
    y = np.array(audio.get_array_of_samples()).astype(np.float32)

    is_stereo = audio.channels == 2
    if is_stereo:
        y = y.reshape((-1, 2))

    delay_samples = int(room_size * audio.frame_rate * 0.2)
    decay = 1.0 - damping

    y_reverb = np.zeros_like(y)
    for i in range(1, 5):
        delay = int(delay_samples * i)
        if delay >= len(y):
            continue
        amplitude = decay ** i
        if is_stereo:
            y_reverb[delay:, 0] += y[:-delay, 0] * amplitude * wet_level
            y_reverb[delay:, 1] += y[:-delay, 1] * amplitude * wet_level
        else:
            y_reverb[delay:] += y[:-delay] * amplitude * wet_level

    y_out = np.clip(y * dry_level + y_reverb, -np.iinfo(np.int16).max, np.iinfo(np.int16).max).astype(np.int16)
    return audio._spawn(y_out)

# ==================== FastAPI 应用初始化 ====================

app = FastAPI(
    title="木偶AI翻唱 API",
    description="AI音色转换和音频处理服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ==================== API 端点定义 ====================

@app.get("/")
async def root():
    """根端点"""
    return {"message": "木偶AI翻唱 API 服务", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": time.time()}

# ==================== 文件管理端点 ====================

@app.post("/upload_audio/")
async def upload_audio(file: UploadFile = File(...)):
    """上传音频文件"""
    global current_audio_file

    # 检查文件类型
    if not file.filename.lower().endswith(('.wav', '.mp3', '.ogg')):
        raise HTTPException(status_code=400, detail="不支持的音频格式，请上传WAV、MP3或OGG文件")

    try:
        # 清理之前的上传文件
        if current_audio_file and os.path.exists(current_audio_file):
            os.unlink(current_audio_file)

        # 保存新文件
        timestamp = int(time.time())
        filename = f"uploaded_{timestamp}_{file.filename}"
        filepath = os.path.join("input", filename)

        with open(filepath, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        current_audio_file = filepath

        # 获取音频信息
        try:
            audio = AudioSegment.from_file(filepath)
            duration = len(audio) / 1000.0  # 转换为秒
            sample_rate = audio.frame_rate
            channels = audio.channels
        except Exception as e:
            logger.warning(f"无法获取音频信息: {str(e)}")
            duration = 0
            sample_rate = 0
            channels = 0

        return {
            "message": "文件上传成功",
            "filename": file.filename,
            "filepath": filepath,
            "size": len(content),
            "duration": duration,
            "sample_rate": sample_rate,
            "channels": channels
        }

    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.get("/selected_audio/")
async def get_selected_audio():
    """获取当前选定的音频文件信息"""
    global current_audio_file

    if not current_audio_file or not os.path.exists(current_audio_file):
        return {"message": "未选择音频文件", "filepath": None}

    try:
        # 获取文件信息
        stat = os.stat(current_audio_file)

        # 获取音频信息
        try:
            audio = AudioSegment.from_file(current_audio_file)
            duration = len(audio) / 1000.0
            sample_rate = audio.frame_rate
            channels = audio.channels
        except Exception as e:
            logger.warning(f"无法获取音频信息: {str(e)}")
            duration = 0
            sample_rate = 0
            channels = 0

        return {
            "filepath": current_audio_file,
            "filename": os.path.basename(current_audio_file),
            "size": stat.st_size,
            "duration": duration,
            "sample_rate": sample_rate,
            "channels": channels,
            "modified_time": stat.st_mtime
        }

    except Exception as e:
        logger.error(f"获取音频信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取音频信息失败: {str(e)}")

@app.delete("/selected_audio/")
async def clear_selected_audio():
    """清除当前选定的音频文件"""
    global current_audio_file

    if current_audio_file and os.path.exists(current_audio_file):
        try:
            os.unlink(current_audio_file)
            logger.info(f"已删除音频文件: {current_audio_file}")
        except Exception as e:
            logger.warning(f"删除音频文件失败: {str(e)}")

    current_audio_file = None
    return {"message": "已清除选定的音频文件"}

@app.get("/download_file/")
async def download_file(file_path: str):
    """下载指定的文件"""
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")

    if not os.path.isfile(file_path):
        raise HTTPException(status_code=400, detail="指定路径不是文件")

    # 安全检查：确保文件在允许的目录内
    allowed_dirs = ["outputs", "results"]
    file_abs_path = os.path.abspath(file_path)
    workspace_abs_path = os.path.abspath(".")

    is_allowed = False
    for allowed_dir in allowed_dirs:
        allowed_abs_path = os.path.abspath(allowed_dir)
        if file_abs_path.startswith(allowed_abs_path):
            is_allowed = True
            break

    if not is_allowed:
        raise HTTPException(status_code=403, detail="不允许访问此文件")

    return FileResponse(
        path=file_path,
        filename=os.path.basename(file_path),
        media_type='application/octet-stream'
    )

# ==================== 模型和配置管理端点 ====================

@app.get("/models/")
async def get_models():
    """获取所有可用的模型文件"""
    models = get_files_from_models_dir(".pt")
    if not models:
        return {"models": [], "message": "未找到模型文件"}
    return {"models": models}

@app.get("/configs/")
async def get_configs():
    """获取所有可用的配置文件"""
    configs = get_files_from_models_dir(".yaml")
    if not configs:
        return {"configs": [], "message": "未找到配置文件"}
    return {"configs": configs}

# ==================== 处理任务管理端点 ====================

@app.post("/process/start/")
async def start_processing(params: ProcessingParams, background_tasks: BackgroundTasks):
    """启动音频处理任务"""
    global current_audio_file

    # 检查是否有音频文件
    if not current_audio_file or not os.path.exists(current_audio_file):
        raise HTTPException(status_code=400, detail="请先上传音频文件")

    # 检查模型文件
    model_path = os.path.join("models", params.model_file)
    if not os.path.exists(model_path):
        raise HTTPException(status_code=400, detail=f"模型文件不存在: {params.model_file}")

    # 检查配置文件（如果指定）
    if params.config_file:
        config_path = os.path.join("models", params.config_file)
        if not os.path.exists(config_path):
            raise HTTPException(status_code=400, detail=f"配置文件不存在: {params.config_file}")

    # 生成任务ID
    task_id = generate_task_id()

    # 创建任务状态
    task_status = TaskStatus(
        task_id=task_id,
        status="pending",
        message="任务已创建，等待开始处理"
    )
    tasks[task_id] = task_status

    # 在后台启动处理任务
    background_tasks.add_task(process_audio_task, task_id, current_audio_file, params)

    return {"task_id": task_id, "message": "处理任务已启动"}

@app.post("/process/stop/")
async def stop_processing(task_id: str):
    """停止指定的处理任务"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = tasks[task_id]
    if task.status not in ["pending", "processing"]:
        raise HTTPException(status_code=400, detail="任务无法停止，当前状态: " + task.status)

    # 停止相关进程
    stop_task_processes(task_id)

    # 更新任务状态
    task.status = "stopped"
    task.message = "任务已被用户停止"

    return {"message": f"任务 {task_id} 已停止"}

@app.get("/process/status/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    return tasks[task_id]

@app.get("/process/results/{task_id}")
async def get_task_results(task_id: str):
    """获取任务结果"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = tasks[task_id]
    if task.status != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成")

    if not task.result:
        raise HTTPException(status_code=404, detail="任务结果不存在")

    return task.result
