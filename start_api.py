#!/usr/bin/env python3
"""
木偶AI翻唱 FastAPI 服务启动脚本
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'pydub',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_directories():
    """检查必要的目录是否存在"""
    required_dirs = ['models', 'input', 'results', 'outputs']
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"✓ 创建目录: {dir_name}")
        else:
            print(f"✓ 目录已存在: {dir_name}")

def check_models():
    """检查模型文件是否存在"""
    models_dir = "models"
    if not os.path.exists(models_dir):
        print("⚠️  models目录不存在")
        return False
    
    pt_files = [f for f in os.listdir(models_dir) if f.endswith('.pt')]
    yaml_files = [f for f in os.listdir(models_dir) if f.endswith('.yaml')]
    
    print(f"✓ 找到 {len(pt_files)} 个模型文件(.pt)")
    print(f"✓ 找到 {len(yaml_files)} 个配置文件(.yaml)")
    
    if len(pt_files) == 0:
        print("⚠️  未找到模型文件，请将.pt文件放入models目录")
        return False
    
    return True

def start_api_server():
    """启动API服务器"""
    print("\n" + "="*50)
    print("启动木偶AI翻唱 FastAPI 服务...")
    print("="*50)
    
    try:
        # 启动uvicorn服务器
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main_api:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print("执行命令:", " ".join(cmd))
        print("\n服务将在以下地址启动:")
        print("  - 本地访问: http://localhost:8000")
        print("  - API文档: http://localhost:8000/docs")
        print("  - 健康检查: http://localhost:8000/health")
        print("\n按 Ctrl+C 停止服务")
        print("-"*50)
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n\n服务已停止")
    except Exception as e:
        print(f"\n启动服务失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("木偶AI翻唱 FastAPI 服务启动器")
    print("="*50)
    
    # 检查依赖
    print("\n1. 检查依赖包...")
    if not check_dependencies():
        return
    
    # 检查目录
    print("\n2. 检查目录结构...")
    check_directories()
    
    # 检查模型
    print("\n3. 检查模型文件...")
    check_models()
    
    # 启动服务
    start_api_server()

if __name__ == "__main__":
    main()
