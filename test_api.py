"""
测试main_api.py的基本功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查端点"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"健康检查: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_models():
    """测试获取模型列表"""
    try:
        response = requests.get(f"{BASE_URL}/models/")
        print(f"模型列表: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取模型列表失败: {e}")
        return False

def test_configs():
    """测试获取配置列表"""
    try:
        response = requests.get(f"{BASE_URL}/configs/")
        print(f"配置列表: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取配置列表失败: {e}")
        return False

def test_songs():
    """测试获取歌曲列表"""
    try:
        response = requests.get(f"{BASE_URL}/songs/")
        print(f"歌曲列表: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取歌曲列表失败: {e}")
        return False

def test_upload_audio():
    """测试音频上传（需要有测试音频文件）"""
    try:
        # 创建一个简单的测试文件
        test_content = b"fake audio content for testing"
        files = {'file': ('test.wav', test_content, 'audio/wav')}
        
        response = requests.post(f"{BASE_URL}/upload_audio/", files=files)
        print(f"音频上传: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"音频上传失败: {e}")
        return False

def test_selected_audio():
    """测试获取选定音频信息"""
    try:
        response = requests.get(f"{BASE_URL}/selected_audio/")
        print(f"选定音频: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取选定音频失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试木偶AI翻唱 API...")
    print("=" * 50)
    
    tests = [
        ("健康检查", test_health),
        ("模型列表", test_models),
        ("配置列表", test_configs),
        ("歌曲列表", test_songs),
        ("选定音频", test_selected_audio),
        ("音频上传", test_upload_audio),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
        print(f"结果: {'✓ 成功' if success else '✗ 失败'}")
    
    print("\n" + "=" * 50)
    print("测试总结:")
    for test_name, success in results:
        status = "✓ 成功" if success else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    print(f"\n总计: {success_count}/{total_count} 个测试通过")

if __name__ == "__main__":
    main()
