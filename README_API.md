# 木偶AI翻唱 FastAPI 后端服务

这是将原PyQt5桌面应用程序转换为FastAPI后端服务的完整实现。所有功能都集成在单一的`main_api.py`文件中。

## 功能特性

- 🎵 音频文件上传和管理
- 🔄 音频分离（人声、伴奏、和声）
- 🎤 AI音色转换
- 🎛️ 音频混音和混响效果
- 📁 成品歌曲管理
- ⚙️ 模型和配置管理
- 🔄 异步任务处理
- 📊 实时任务状态监控

## 安装依赖

```bash
pip install fastapi uvicorn pydantic pydub numpy
```

## 启动服务

```bash
python main_api.py
```

服务将在 `http://localhost:8000` 启动。

## API 端点文档

### 基础端点

- `GET /` - 根端点，返回服务信息
- `GET /health` - 健康检查
- `GET /docs` - Swagger API文档（自动生成）

### 文件管理

- `POST /upload_audio/` - 上传音频文件
- `GET /selected_audio/` - 获取当前选定的音频文件信息
- `DELETE /selected_audio/` - 清除当前选定的音频文件
- `GET /download_file/?file_path=<path>` - 下载指定文件

### 模型和配置

- `GET /models/` - 获取所有可用的模型文件(.pt)
- `GET /configs/` - 获取所有可用的配置文件(.yaml)

### 音频处理

- `POST /process/start/` - 启动音频处理任务
- `POST /process/stop/` - 停止指定的处理任务
- `GET /process/status/{task_id}` - 获取任务状态
- `GET /process/results/{task_id}` - 获取任务结果

### 重新混音

- `POST /remix/` - 重新混音已处理的音频

### 成品歌曲管理

- `GET /songs/` - 获取所有成品歌曲列表
- `GET /songs/{song_name}/files` - 获取指定歌曲的所有音频文件
- `DELETE /songs/{song_name}` - 删除指定的歌曲

## 使用示例

### 1. 上传音频文件

```python
import requests

files = {'file': open('audio.wav', 'rb')}
response = requests.post('http://localhost:8000/upload_audio/', files=files)
print(response.json())
```

### 2. 启动处理任务

```python
import requests

params = {
    "processing_mode": "完整模式",
    "output_format": "WAV",
    "model_file": "voice_model_alpha.pt",
    "config_file": "voice_model_alpha.yaml",
    "vocal_pitch": 0,
    "instrumental_pitch": 0,
    "reverb_enabled": True,
    "room_size": 0.6,
    "damping": 0.1,
    "wet_level": 0.3,
    "dry_level": 0.9,
    "harmony_to_accomp": False,
    "vocoder": "pc_nsf_hifigan_testing",
    "formant_shift": 0,
    "method": "euler",
    "f0_extractor": "rmvpe",
    "infer_steps": 50,
    "device": "CUDA"
}

response = requests.post('http://localhost:8000/process/start/', json=params)
task_id = response.json()['task_id']
print(f"任务ID: {task_id}")
```

### 3. 监控任务状态

```python
import requests
import time

task_id = "your_task_id"

while True:
    response = requests.get(f'http://localhost:8000/process/status/{task_id}')
    status = response.json()
    
    print(f"状态: {status['status']}, 进度: {status['progress']}%, 消息: {status['message']}")
    
    if status['status'] in ['completed', 'failed', 'stopped']:
        break
    
    time.sleep(2)
```

## 处理模式

### 完整模式
1. 音频分离（人声、伴奏、和声）
2. 音色转换
3. 混音处理

### 干声模式
直接对输入音频进行音色转换

## 参数说明

### ProcessingParams

- `processing_mode`: 处理模式（"完整模式" 或 "干声模式"）
- `output_format`: 输出格式（"WAV" 或 "MP3"）
- `model_file`: 音色模型文件名
- `config_file`: 配置文件名（可选）
- `vocal_pitch`: 人声音高调整（-18到18）
- `instrumental_pitch`: 伴奏音高调整（-18到18）
- `reverb_enabled`: 是否启用混响
- `room_size`: 房间大小（0.0到1.0）
- `damping`: 阻尼（0.0到1.0）
- `wet_level`: 湿润度（0.0到1.0）
- `dry_level`: 干燥度（0.0到1.0）
- `harmony_to_accomp`: 和声加入伴奏
- `vocoder`: 声码器
- `formant_shift`: 共振峰偏移（-6到6）
- `method`: 采样器
- `f0_extractor`: F0提取器
- `infer_steps`: 采样步数（1到1000）
- `device`: 设备选择

## 目录结构

```
├── main_api.py          # 主API文件
├── test_api.py          # API测试脚本
├── models/              # 模型文件目录
├── input/               # 输入文件目录
├── results/             # 处理结果目录
├── outputs/             # 最终输出目录
└── ffmpeg/              # FFmpeg工具目录
```

## 测试

运行测试脚本：

```bash
python test_api.py
```

## 注意事项

1. 确保所有必要的目录存在（models, input, results, outputs）
2. 确保FFmpeg已正确安装和配置
3. 确保Python环境中安装了所有依赖包
4. 模型文件需要放在models目录中
5. 处理大文件时可能需要较长时间，请耐心等待

## 错误处理

API会返回标准的HTTP状态码：
- 200: 成功
- 400: 客户端错误（参数错误等）
- 404: 资源不存在
- 500: 服务器内部错误

错误响应格式：
```json
{
    "detail": "错误描述信息"
}
```
